import apiClient from './apiClient';

export interface Video {
  id: string;
  title: string;
  description: string;
  url: string;
  thumbnailUrl: string;
  duration: number;
  createdAt: string;
  updatedAt: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  pageNumber: number;
  totalPages: number;
  totalCount: number;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
}

export interface VideoSearchParams {
  searchText?: string;
  pageNumber?: number;
  pageSize?: number;
  sortBy?: string;
  sortDescending?: boolean;
}

export const searchVideos = async (params: VideoSearchParams = {}) => {
  const response = await apiClient.get<PaginatedResponse<Video>>('/videos/search', {
    params: {
      searchText: params.searchText || '',
      pageNumber: params.pageNumber || 1,
      pageSize: params.pageSize || 10,
      sortBy: params.sortBy || 'createdAt',
      sortDescending: params.sortDescending !== false, // default to true if not specified
    },
  });
  return response.data;
};

// Interfaces for video details with transcript data
export interface WordTiming {
  word: string;
  startTime: number;
  endTime: number;
}

export interface TranscriptSegment {
  id: string;
  text: string;
  startTime: number;
  endTime: number;
  words?: WordTiming[];
}

// Backend VideoDto structure
export interface VideoDto {
  id: number;
  title: string;
  description: string;
  durationSeconds: number;
  transcript: string;
  filePath: string;
  createdAt: string;
}

export interface VideoDetails {
  id: string;
  title: string;
  description: string;
  duration: number;
  transcript: string;
  createdAt: string;
  transcriptSegments: TranscriptSegment[];
}

export const getVideoById = async (id: string): Promise<VideoDetails> => {
  const response = await apiClient.get<VideoDto>(`/videos/${id}`);
  const videoDto = response.data;

  // Transform backend DTO to frontend interface
  const videoDetails: VideoDetails = {
    id: videoDto.id.toString(),
    title: videoDto.title,
    description: videoDto.description,
    duration: videoDto.durationSeconds,
    transcript: videoDto.transcript,
    createdAt: videoDto.createdAt,
    transcriptSegments: [] // For now, empty array - will be populated later when backend supports it
  };

  return videoDetails;
};
