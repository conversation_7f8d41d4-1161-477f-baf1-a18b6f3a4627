import apiClient from './apiClient';

export interface Video {
  id: string;
  title: string;
  description: string;
  url: string;
  thumbnailUrl: string;
  duration: number;
  createdAt: string;
  updatedAt: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  pageNumber: number;
  totalPages: number;
  totalCount: number;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
}

export interface VideoSearchParams {
  searchText?: string;
  pageNumber?: number;
  pageSize?: number;
  sortBy?: string;
  sortDescending?: boolean;
}

export const searchVideos = async (params: VideoSearchParams = {}) => {
  const response = await apiClient.get<PaginatedResponse<Video>>('/videos/search', {
    params: {
      searchText: params.searchText || '',
      pageNumber: params.pageNumber || 1,
      pageSize: params.pageSize || 10,
      sortBy: params.sortBy || 'createdAt',
      sortDescending: params.sortDescending !== false, // default to true if not specified
    },
  });
  return response.data;
};

// Interfaces for video details with transcript data
export interface WordTiming {
  word: string;
  startTime: number;
  endTime: number;
}

export interface TranscriptSegment {
  id: string;
  text: string;
  startTime: number;
  endTime: number;
  words?: WordTiming[];
}

// Backend VideoDto structure
export interface VideoDto {
  id: number;
  title: string;
  description: string;
  durationSeconds: number;
  transcript: string;
  filePath: string;
  createdAt: string;
}

export interface VideoDetails {
  id: string;
  title: string;
  description: string;
  duration: number;
  transcript: string;
  createdAt: string;
  transcriptSegments: TranscriptSegment[];
}

// Backend WordOccurrenceDto structure for transcript segments
export interface WordOccurrenceDto {
  videoId: number;
  videoTitle: string;
  segmentGroupId: number;
  startTime: number;
  endTime: number;
  text: string;
  wordTimings: {
    id: number;
    segmentId: number;
    word: string;
    startTime: number;
    endTime: number;
    wordIndex: number;
  }[];
}

export const getVideoById = async (id: string): Promise<VideoDetails> => {
  console.log('getVideoById called with ID:', id);
  const response = await apiClient.get<VideoDto>(`/videos/${id}`);
  const videoDto = response.data;
  console.log('Video DTO received:', videoDto);

  // Fetch transcript segments with word timings
  let transcriptSegments: TranscriptSegment[] = [];
  try {
    console.log('Fetching transcript segments for video ID:', id);
    const segmentsResponse = await apiClient.get<WordOccurrenceDto[]>(`/videos/${id}/transcript-segments`);
    console.log('Segments response:', segmentsResponse.data);
    transcriptSegments = segmentsResponse.data.map((segment) => ({
      id: segment.segmentGroupId.toString(),
      text: segment.text,
      startTime: segment.startTime,
      endTime: segment.endTime,
      words: segment.wordTimings.map(wt => ({
        word: wt.word,
        startTime: wt.startTime,
        endTime: wt.endTime
      }))
    }));
    console.log('Mapped transcript segments:', transcriptSegments);
  } catch (error) {
    console.error('Failed to fetch transcript segments:', error);
    if (error && typeof error === 'object' && 'response' in error) {
      console.error('Error details:', (error as any).response?.data);
      console.error('Error status:', (error as any).response?.status);
    }
    // Continue with empty segments if the endpoint fails
  }

  // TEMPORARY: Add test data if no segments were loaded
  if (transcriptSegments.length === 0) {
    console.log('No segments loaded, adding test data');
    transcriptSegments = [{
      id: "test-1",
      text: "In the beginning was the Word",
      startTime: 0,
      endTime: 30,
      words: [
        { word: "In", startTime: 0, endTime: 2 },
        { word: "the", startTime: 2, endTime: 4 },
        { word: "beginning", startTime: 4, endTime: 8 },
        { word: "was", startTime: 8, endTime: 10 },
        { word: "the", startTime: 10, endTime: 12 },
        { word: "Word", startTime: 12, endTime: 15 }
      ]
    }];
  }

  // Transform backend DTO to frontend interface
  const videoDetails: VideoDetails = {
    id: videoDto.id.toString(),
    title: videoDto.title,
    description: videoDto.description,
    duration: videoDto.durationSeconds,
    transcript: videoDto.transcript,
    createdAt: videoDto.createdAt,
    transcriptSegments
  };

  return videoDetails;
};
